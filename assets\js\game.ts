import { _decorator, Component, Node, SpriteFrame, Texture2D, resources, Rect, Size } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('game')
export class game extends Component {

    // 雪碧图配置
    private readonly SPRITE_WIDTH = 230;
    private readonly SPRITE_HEIGHT = 520;
    private readonly TOTAL_SPRITES = 12;
    private readonly COLS = 4; // 4列
    private readonly ROWS = 3; // 3行

    // 存储所有的SpriteFrame
    private bottleFrames: SpriteFrame[] = [];

    start() {
        this.loadBottleSprites();
    }

    /**
     * 加载bottle.png雪碧图并切割成12个SpriteFrame
     */
    private loadBottleSprites() {
        // 加载bottle.png纹理
        resources.load('img/bottle', Texture2D, (err, texture) => {
            if (err) {
                console.error('加载bottle.png失败:', err);
                return;
            }

            console.log('成功加载bottle.png纹理');
            this.createSpriteFrames(texture);
        });
    }

    /**
     * 从纹理创建SpriteFrame数组
     * @param texture 加载的纹理
     */
    private createSpriteFrames(texture: Texture2D) {
        this.bottleFrames = [];

        for (let i = 0; i < this.TOTAL_SPRITES; i++) {
            // 计算当前精灵在雪碧图中的位置
            const col = i % this.COLS;
            const row = Math.floor(i / this.COLS);

            // 计算裁剪区域
            const x = col * this.SPRITE_WIDTH;
            const y = row * this.SPRITE_HEIGHT;

            // 创建SpriteFrame
            const spriteFrame = new SpriteFrame();
            spriteFrame.texture = texture;

            // 设置裁剪区域 (注意Cocos Creator的坐标系是左下角为原点)
            const rect = new Rect(x, y, this.SPRITE_WIDTH, this.SPRITE_HEIGHT);
            spriteFrame.rect = rect;

            // 设置原始尺寸
            spriteFrame.originalSize = new Size(this.SPRITE_WIDTH, this.SPRITE_HEIGHT);

            this.bottleFrames.push(spriteFrame);

            console.log(`创建第${i + 1}个bottle精灵帧，位置: (${x}, ${y})`);
        }

        console.log(`成功创建${this.bottleFrames.length}个bottle精灵帧`);

        // 创建完成后的示例用法
        this.demonstrateUsage();
    }

    /**
     * 演示如何使用创建的SpriteFrame
     */
    private demonstrateUsage() {
        // 示例：获取第一个bottle精灵帧
        const firstBottleFrame = this.getBottleFrame(0);
        if (firstBottleFrame) {
            console.log('获取到第一个bottle精灵帧');
            // 这里可以将精灵帧应用到Sprite组件上
            // 例如: someSprite.spriteFrame = firstBottleFrame;
        }
    }

    /**
     * 获取指定索引的bottle精灵帧
     * @param index 索引 (0-11)
     * @returns SpriteFrame或null
     */
    public getBottleFrame(index: number): SpriteFrame | null {
        if (index < 0 || index >= this.bottleFrames.length) {
            console.warn(`无效的bottle索引: ${index}, 有效范围: 0-${this.bottleFrames.length - 1}`);
            return null;
        }
        return this.bottleFrames[index];
    }

    /**
     * 获取所有bottle精灵帧
     * @returns SpriteFrame数组
     */
    public getAllBottleFrames(): SpriteFrame[] {
        return [...this.bottleFrames]; // 返回副本
    }

    /**
     * 获取随机的bottle精灵帧
     * @returns 随机的SpriteFrame
     */
    public getRandomBottleFrame(): SpriteFrame | null {
        if (this.bottleFrames.length === 0) {
            return null;
        }
        const randomIndex = Math.floor(Math.random() * this.bottleFrames.length);
        return this.bottleFrames[randomIndex];
    }

    update(deltaTime: number) {

    }
}


